{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerProfiles/addressValidation.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from \"@/lib/utils/addressValidation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n/**\r\n * Checks if customer has complete address information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerAddress(userId: string): Promise<{\r\n  isValid: boolean;\r\n  missingFields?: string[];\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    // Fetch customer address data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer profile for address validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your address information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n      };\r\n    }\r\n    \r\n    const addressData: CustomerAddressData = {\r\n      pincode: profile?.pincode,\r\n      state: profile?.state,\r\n      city: profile?.city,\r\n      locality: profile?.locality,\r\n      address: profile?.address\r\n    };\r\n    \r\n    const isValid = isCustomerAddressComplete(addressData);\r\n    \r\n    if (!isValid) {\r\n      const missingFields = getMissingAddressFields(addressData);\r\n      const message = getAddressValidationMessage(missingFields);\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n      \r\n      return {\r\n        isValid: false,\r\n        missingFields,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n    \r\n    return { isValid: true };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error during address validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your address. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check address and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteAddress(userId: string): Promise<void> {\r\n  const validation = await validateCustomerAddress(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if customer has complete name information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerName(userId: string): Promise<{\r\n  isValid: boolean;\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Fetch customer name data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name')\r\n      .eq('id', userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error('Error fetching customer profile for name validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your profile information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n      };\r\n    }\r\n\r\n    // Check if name is present and not empty\r\n    const isValid = !!(profile?.name && profile.name.trim() !== '');\r\n\r\n    if (!isValid) {\r\n      const message = 'Please complete your name in your profile to access the dashboard.';\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n\r\n      return {\r\n        isValid: false,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error during name validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your profile. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check name and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteName(userId: string): Promise<void> {\r\n  const validation = await validateCustomerName(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check both address and name, redirect if incomplete\r\n * Use this in customer dashboard pages (except settings page)\r\n * Settings page is exempt from address validation\r\n */\r\nexport async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {\r\n  // Always check name (required for all dashboard access)\r\n  await requireCompleteName(userId);\r\n\r\n  // Only check address if not exempt (settings page is exempt)\r\n  if (!exemptFromAddressValidation) {\r\n    await requireCompleteAddress(userId);\r\n  }\r\n}\r\n\r\n/**\r\n * Get customer address data for forms\r\n */\r\nexport async function getCustomerAddressData(userId: string): Promise<{\r\n  data?: CustomerAddressData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer address data:', error);\r\n      return { error: 'Failed to fetch address data' };\r\n    }\r\n    \r\n    return {\r\n      data: {\r\n        pincode: profile?.pincode,\r\n        state: profile?.state,\r\n        city: profile?.city,\r\n        locality: profile?.locality,\r\n        address: profile?.address\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error fetching address data:', error);\r\n    return { error: 'An unexpected error occurred' };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;;;AAMO,eAAe,wBAAwB,MAAc;IAM1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2DAA2D;YACzE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,MAAM,cAAmC;YACvC,SAAS,SAAS;YAClB,OAAO,SAAS;YAChB,MAAM,SAAS;YACf,UAAU,SAAS;YACnB,SAAS,SAAS;QACpB;QAEA,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC5C,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,uBAAuB,MAAc;IACzD,MAAM,aAAa,MAAM,wBAAwB;IAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAMO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,2BAA2B;QAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wDAAwD;YACtE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,yCAAyC;QACzC,MAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;QAE9D,IAAI,CAAC,SAAS;YACZ,MAAM,UAAU;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,oBAAoB,MAAc;IACtD,MAAM,aAAa,MAAM,qBAAqB;IAE9C,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAOO,eAAe,uBAAuB,MAAc,EAAE,8BAAuC,KAAK;IACvG,wDAAwD;IACxD,MAAM,oBAAoB;IAE1B,6DAA6D;IAC7D,IAAI,CAAC,6BAA6B;QAChC,MAAM,uBAAuB;IAC/B;AACF;AAKO,eAAe,uBAAuB,MAAc;IAIzD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,OAAO;YAA+B;QACjD;QAEA,OAAO;YACL,MAAM;gBACJ,SAAS,SAAS;gBAClB,OAAO,SAAS;gBAChB,MAAM,SAAS;gBACf,UAAU,SAAS;gBACnB,SAAS,SAAS;YACpB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IA9LsB;IAiEA;IAYA;IAuDA;IAaA;IAaA;;AA9JA,+OAAA;AAiEA,+OAAA;AAYA,+OAAA;AAuDA,+OAAA;AAaA,+OAAA;AAaA,+OAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForSubscribe = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForSubscribe\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForUnsubscribe = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnsubscribe\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use admin client to bypass RLS\r\n    const supabaseAdminForReview = createAdminClient();\r\n    const { error: upsertError } = await supabaseAdminForReview\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForDeleteReview = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForDeleteReview\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use admin client to bypass RLS\r\n    const supabaseAdminForLike = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForLike\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForSlug = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForSlug\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use admin client to bypass RLS\r\n    const supabaseAdminForUnlike = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnlike\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForCardData = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForCardData\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForStatus = createAdminClient();\r\n\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabaseAdminForStatus\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;;;;;;;AAKO,eAAe,oBACpB,iBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,iEAAiE;IACjE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,0DAA0D;QAC1D,MAAM,4BAA4B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,0BAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,+EAA+E;YAC/E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,CAAC;gBAExE,qEAAqE;gBACrE,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,yEAAyE;QACzE,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;QAEvE,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAIO,eAAe,wBACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qEAAqE;IACrE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,0DAA0D;QAC1D,MAAM,8BAA8B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACpD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,4BAClC,IAAI,CAAC,iBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB,EACzB,MAAc,EACd,UAA0B,AAAC,6BAA6B;;IAExD,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,4DAA4D;IAC5D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,SAAS,KAAK,SAAS,GAAG;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkC;IACpE;IAEA,IAAI;QACF,6FAA6F;QAC7F,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,uBAClC,IAAI,CAAC,mBACL,MAAM,CACL;YACE,SAAS,KAAK,EAAE;YAChB,qBAAqB;YACrB,QAAQ;YACR,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACA;YACE,YAAY;QACd;QAGJ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6DAA6D;QAEpG,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAM,+BAA+B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACrD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,6BAClC,IAAI,CAAC,mBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,yDAAyD;IACzD,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,uBAAuB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC7C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,qBAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,0EAA0E;YAC1E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;gBAEhE,OAAO;oBAAE,SAAS;gBAAK,GAAG,uCAAuC;YACnE;YACA,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,uBAAuB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC7C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,qBAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,eACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,2DAA2D;IAC3D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,uBAClC,IAAI,CAAC,SACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,2BAA2B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACjD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yBAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,qBAAqB,iBAAyB;IAOlE,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAClE,IAAI,SAAwB;IAE5B,mEAAmE;IACnE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,MAAM;QACR,SAAS,KAAK,EAAE;IAClB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,cAAc;QACd,UAAU;QACV,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO,eAAe,yCAAyC;IACjE;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAE/C,iCAAiC;QACjC,MAAM,CAAC,iBAAiB,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,uBACG,IAAI,CAAC,iBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,uBACG,IAAI,CAAC,SACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,uBACG,IAAI,CAAC,mBACL,MAAM,CAAC,uBACP,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB,GAChE,WAAW;SACf;QAED,uCAAuC;QACvC,IAAI,gBAAgB,KAAK,EACvB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,OAAO,EAAE;QAEhE,IAAI,QAAQ,KAAK,EACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE;QAC9D,IAAI,UAAU,KAAK,EACjB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAElE,MAAM,aAAa,UAAU,IAAI;QAEjC,OAAO;YACL,cAAc,CAAC,gBAAgB,KAAK,IAAI,CAAC,IAAI;YAC7C,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACjC,YAAY,YAAY,UAAU;YAClC,YAAY,YAAY,eAAe;QACzC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,sDAAsD;QACtD,OAAO;YAAE,GAAG,aAAa;YAAE,OAAO;QAAa;IACjD;AACF;;;IArhBsB;IAyFA;IA8EA;IA6EA;IAwDA;IAgFA;IA4EA;;AAxcA,+OAAA;AAyFA,+OAAA;AA8EA,+OAAA;AA6EA,+OAAA;AAwDA,+OAAA;AAgFA,+OAAA;AA4EA,+OAAA", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/likes/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {validateCustomerAddress as '4044cbba3f28f10081025b33e838df6e3ddc0072ca'} from 'ACTIONS_MODULE1'\nexport {validateCustomerName as '404fd0a9be6e93cb26858695096fb8f7a80e566a2c'} from 'ACTIONS_MODULE1'\nexport {getCustomerAddressData as '407b620db7ebb4e0475b90bfef8276a8d79b4bb51a'} from 'ACTIONS_MODULE1'\nexport {requireCompleteAddress as '40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868'} from 'ACTIONS_MODULE1'\nexport {requireCompleteName as '40e02d24852c03895746a18f4b2a7e50cb5b140aa4'} from 'ACTIONS_MODULE1'\nexport {requireCompleteProfile as '60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab'} from 'ACTIONS_MODULE1'\nexport {unlikeBusiness as '40e7895cc46e1fab4c2aa1252e79af5026580650a1'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AAMA", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/likes/components/LikesPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/likes/components/LikesPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/likes/components/LikesPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2U,GACxW,yGACA", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/likes/components/LikesPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/likes/components/LikesPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/likes/components/LikesPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuT,GACpV,qFACA", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeCardSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LikeListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call LikeListSkeleton() from the server but LikeListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCardSkeleton.tsx <module evaluation>\",\n    \"LikeListSkeleton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeCardSkeleton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCardSkeleton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kFACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeCardSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LikeListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call LikeListSkeleton() from the server but LikeListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCardSkeleton.tsx\",\n    \"LikeListSkeleton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeCardSkeleton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeCardSkeleton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8DACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeSearch.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeSearch.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeSearch.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeSearch.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeSearch.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeSearch.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikePagination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikePagination.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikePagination.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikePagination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikePagination.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikePagination.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/LikeList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/likes/LikeList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/likes/LikeList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/likes/index.ts"], "sourcesContent": ["// Export all shared like components\r\nexport { default as LikeCard } from './LikeCard';\r\nexport { default as LikeCardSkeleton, LikeListSkeleton } from './LikeCardSkeleton';\r\nexport { default as LikeSearch } from './LikeSearch';\r\nexport { default as LikePagination } from './LikePagination';\r\nexport { default as LikeList } from './LikeList';\r\n\r\n// Export types\r\nexport type { ProfileData, LikeData } from './LikeCard';\r\n"], "names": [], "mappings": "AAAA,oCAAoC;;AACpC;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/likes/actions.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\n\r\n// Define interfaces for the expected data structure\r\ninterface BusinessProfileDataForLike {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n  city: string | null;\r\n  state: string | null;\r\n  pincode: string | null;\r\n  address_line: string | null;\r\n}\r\n\r\nexport interface LikeWithProfile {\r\n  id: string;\r\n  business_profiles: BusinessProfileDataForLike | null;\r\n}\r\n\r\ninterface LikesResult {\r\n  items: LikeWithProfile[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n/**\r\n * Fetch customer likes with pagination and search\r\n */\r\nexport async function fetchCustomerLikes(\r\n  userId: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  searchTerm: string = \"\"\r\n): Promise<LikesResult> {\r\n  const _supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  try {\r\n    // Build the query with proper joins and filtering\r\n    let query = supabaseAdmin\r\n      .from('likes')\r\n      .select(`\r\n        id,\r\n        business_profiles!inner (\r\n          id,\r\n          business_name,\r\n          business_slug,\r\n          logo_url,\r\n          city,\r\n          state,\r\n          pincode,\r\n          address_line\r\n        )\r\n      `)\r\n      .eq('user_id', userId);\r\n\r\n    // Apply search filter if provided\r\n    if (searchTerm) {\r\n      query = query.ilike('business_profiles.business_name', `%${searchTerm}%`);\r\n    }\r\n\r\n    // Get total count for pagination with proper join for search\r\n    let countQuery = supabaseAdmin\r\n      .from('likes')\r\n      .select(`\r\n        id,\r\n        business_profiles!inner (\r\n          id,\r\n          business_name\r\n        )\r\n      `, { count: 'exact', head: true })\r\n      .eq('user_id', userId);\r\n\r\n    // Apply search filter to count query if provided\r\n    if (searchTerm) {\r\n      countQuery = countQuery.ilike('business_profiles.business_name', `%${searchTerm}%`);\r\n    }\r\n\r\n    const { count: totalCount, error: countError } = await countQuery;\r\n\r\n    if (countError) {\r\n      throw new Error(\"Failed to get total count\");\r\n    }\r\n\r\n    // If no likes, return empty result\r\n    if (!totalCount || totalCount === 0) {\r\n      return {\r\n        items: [],\r\n        totalCount: 0,\r\n        hasMore: false,\r\n        currentPage: page\r\n      };\r\n    }\r\n\r\n    // Apply pagination to the query\r\n    const from = (page - 1) * limit;\r\n    query = query.range(from, from + limit - 1);\r\n\r\n    const { data: likesWithProfiles, error: likesError } = await query;\r\n\r\n    if (likesError) {\r\n      throw new Error(\"Failed to fetch likes\");\r\n    }\r\n\r\n    // Transform the data to match LikeWithProfile interface\r\n    const transformedItems: LikeWithProfile[] = (likesWithProfiles || []).map(item => ({\r\n      id: item.id,\r\n      business_profiles: Array.isArray(item.business_profiles)\r\n        ? item.business_profiles[0] || null\r\n        : item.business_profiles\r\n    }));\r\n\r\n    const hasMore = totalCount > from + limit;\r\n\r\n    return {\r\n      items: transformedItems,\r\n      totalCount,\r\n      hasMore,\r\n      currentPage: page\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in fetchCustomerLikes:', error);\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA6BO,eAAe,mBACpB,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;IAEvB,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IACnC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,IAAI;QACF,kDAAkD;QAClD,IAAI,QAAQ,cACT,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;MAYT,CAAC,EACA,EAAE,CAAC,WAAW;QAEjB,kCAAkC;QAClC,IAAI,YAAY;YACd,QAAQ,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC1E;QAEA,6DAA6D;QAC7D,IAAI,aAAa,cACd,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EAAE;YAAE,OAAO;YAAS,MAAM;QAAK,GAC/B,EAAE,CAAC,WAAW;QAEjB,iDAAiD;QACjD,IAAI,YAAY;YACd,aAAa,WAAW,KAAK,CAAC,mCAAmC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACpF;QAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;QAEvD,IAAI,YAAY;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,CAAC,cAAc,eAAe,GAAG;YACnC,OAAO;gBACL,OAAO,EAAE;gBACT,YAAY;gBACZ,SAAS;gBACT,aAAa;YACf;QACF;QAEA,gCAAgC;QAChC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,QAAQ,MAAM,KAAK,CAAC,MAAM,OAAO,QAAQ;QAEzC,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;QAE7D,IAAI,YAAY;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,MAAM,mBAAsC,CAAC,qBAAqB,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACjF,IAAI,KAAK,EAAE;gBACX,mBAAmB,MAAM,OAAO,CAAC,KAAK,iBAAiB,IACnD,KAAK,iBAAiB,CAAC,EAAE,IAAI,OAC7B,KAAK,iBAAiB;YAC5B,CAAC;QAED,MAAM,UAAU,aAAa,OAAO;QAEpC,OAAO;YACL,OAAO;YACP;YACA;YACA,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/likes/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\n\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Al<PERSON><PERSON>riangle, Heart } from 'lucide-react';\r\nimport LikesPageClient from './components/LikesPageClient';\r\nimport { Suspense } from 'react';\r\nimport { LikeListSkeleton } from '@/app/components/shared/likes';\r\nimport { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';\r\n\r\n// Import the fetchCustomerLikes function\r\nimport { fetchCustomerLikes } from './actions';\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"My Likes - Dukancard\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function CustomerLikesPage({\r\n  searchParams\r\n}: {\r\n  searchParams: Promise<{ search?: string; page?: string }>\r\n}) {\r\n  // Properly await searchParams to fix the error\r\n  const { search, page: pageParam } = await searchParams;\r\n  const supabase = await createClient();\r\n  const page = pageParam ? parseInt(pageParam) : 1;\r\n  const searchTerm = search || \"\";\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your likes.');\r\n  }\r\n\r\n  // Check if customer has complete address\r\n  await requireCompleteProfile(user.id);\r\n\r\n  try {\r\n    // Fetch likes with pagination and search\r\n    const likesResult = await fetchCustomerLikes(user.id, page, 10, searchTerm);\r\n\r\n    // Full width layout without card wrapper\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Suspense fallback={\r\n          <div className=\"space-y-6\">\r\n            {/* Header Section with proper layout */}\r\n            <div className=\"flex flex-col gap-6\">\r\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n                    <Heart className=\"w-6 h-6 text-rose-600 dark:text-rose-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h1 className=\"text-2xl font-bold text-foreground\">\r\n                      Liked Businesses\r\n                    </h1>\r\n                    <p className=\"text-muted-foreground mt-1\">\r\n                      Businesses you&apos;ve liked\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Search skeleton - aligned to the right on desktop */}\r\n                <div className=\"w-full sm:w-80\">\r\n                  <Skeleton className=\"h-10 w-full rounded-md\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Content skeleton */}\r\n            <LikeListSkeleton />\r\n          </div>\r\n        }>\r\n          <LikesPageClient\r\n            initialLikes={likesResult.items}\r\n            totalCount={likesResult.totalCount}\r\n            currentPage={likesResult.currentPage}\r\n            searchTerm={searchTerm}\r\n          />\r\n        </Suspense>\r\n      </div>\r\n    );\r\n  } catch (_error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertTriangle className=\"h-4 w-4\" />\r\n        <AlertTitle>Error</AlertTitle>\r\n        <AlertDescription>\r\n          Could not load likes data. Please try again later.\r\n        </AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA,yCAAyC;AACzC;;;;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe,kBAAkB,EAC9C,YAAY,EAGb;IACC,+CAA+C;IAC/C,MAAM,EAAE,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,OAAO,YAAY,SAAS,aAAa;IAC/C,MAAM,aAAa,UAAU;IAE7B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yCAAyC;IACzC,MAAM,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;IAEpC,IAAI;QACF,yCAAyC;QACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,EAAE,EAAE,MAAM,IAAI;QAEhE,yCAAyC;QACzC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBACR,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEAGnD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAO9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAM1B,8OAAC,yJAAA,CAAA,mBAAgB;;;;;;;;;;;0BAGnB,cAAA,8OAAC,wLAAA,CAAA,UAAe;oBACd,cAAc,YAAY,KAAK;oBAC/B,YAAY,YAAY,UAAU;oBAClC,aAAa,YAAY,WAAW;oBACpC,YAAY;;;;;;;;;;;;;;;;IAKtB,EAAE,OAAO,QAAQ;QACf,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAQ;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC,0HAAA,CAAA,aAAU;8BAAC;;;;;;8BACZ,8OAAC,0HAAA,CAAA,mBAAgB;8BAAC;;;;;;;;;;;;IAKxB;AACF", "debugId": null}}]}