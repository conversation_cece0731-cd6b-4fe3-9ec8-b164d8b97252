'use client';


import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ModernFeedContainerProps {
  children: ReactNode;
  className?: string;
}

export default function ModernFeedContainer({ children, className }: ModernFeedContainerProps) {
  return (
    <div className={cn("min-h-screen bg-background", className)}>
      <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
        {children}
      </div>
    </div>
  );
}
